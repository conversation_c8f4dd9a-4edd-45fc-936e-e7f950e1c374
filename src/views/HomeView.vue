<template>
  <div class="home-view">
    <!-- 侧边栏导航 -->
    <SidebarNavigation :current-slide="currentSlide" :active-tab="activeTab" :main-swiper-instance="mainSwiperInstance"
      @slide-change="onSlideChange" @tab-change="switchTab" />

    <!-- 主要垂直滚屏容器 -->
    <VerticalSwiper :slide-count="7" :show-navigation="false" custom-class="main-swiper" @slide-change="onSlideChange"
      @swiper-ready="onMainSwiperReady">
      <!-- 第1屏 - 首页 -->
      <swiper-slide class="slide-item slide-1">
        <div class="slide-content">
        </div>
      </swiper-slide>

      <!-- 第2屏 - 预约 -->
      <swiper-slide class="slide-item slide-2">
        <div class="slide-content">
        </div>
      </swiper-slide>

      <!-- 第3屏 - 抽奖 -->
      <swiper-slide class="slide-item slide-3">
        <div class="slide-content">
        </div>
      </swiper-slide>

      <!-- 第4屏 - 里程碑 -->
      <swiper-slide class="slide-item slide-4">
        <div class="slide-content">
        </div>
      </swiper-slide>

      <!-- 第5屏 - 职业介绍 -->
      <swiper-slide class="slide-item slide-5">
        <div class="slide-content">
        </div>
      </swiper-slide>

      <!-- 第6屏 - 公会 -->
      <swiper-slide class="slide-item slide-6">
        <div class="slide-content">
        </div>
      </swiper-slide>

      <!-- 第7屏 - 游戏世界观介绍 -->
      <swiper-slide class="slide-item slide-7">
        <div class="slide-content">
          <div class="world-view-container">

            <!-- Tab 切换导航 -->
            <div class="tab-navigation">
              <button :class="['tab-btn', { active: activeTab === 'tab1' }]" @click="switchTab('tab1')"
                :style="{ backgroundImage: `url(${getTabImage('tab1')})` }">
              </button>
              <button :class="['tab-btn', { active: activeTab === 'tab2' }]" @click="switchTab('tab2')"
                :style="{ backgroundImage: `url(${getTabImage('tab2')})` }">
              </button>
            </div>

            <!-- Tab 内容区域 -->
            <div class="tab-content">
              <!-- Tab1: 水平全屏轮播 -->
              <Transition name="tab-fade" mode="out-in">
                <div v-if="activeTab === 'tab1'" key="tab1" class="tab-panel tab1-panel">
                  <HorizontalSwiper @slide-change="onWorldSwiperChange" @swiper-ready="onWorldSwiperReady">
                    <swiper-slide v-for="(image, index) in worldImages" :key="index" class="horizontal-slide"
                      :style="{ backgroundImage: `url(${image})` }">
                      <div class="horizontal-slide-content">
                      </div>
                    </swiper-slide>
                  </HorizontalSwiper>
                </div>

                <!-- Tab2: 固定背景 + 居中轮播 -->
                <div v-else-if="activeTab === 'tab2'" key="tab2" class="tab-panel tab2-panel">
                  <StorySwiper :stories="storyData" @slide-change="onStorySwiperChange"
                    @swiper-ready="onStorySwiperReady" />
                </div>
              </Transition>
            </div>
          </div>
        </div>
      </swiper-slide>
    </VerticalSwiper>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { SwiperSlide } from 'swiper/vue'

// 导入自定义组件
import VerticalSwiper from '@/components/swiper/VerticalSwiper.vue'
import HorizontalSwiper from '@/components/swiper/HorizontalSwiper.vue'
import StorySwiper from '@/components/swiper/StorySwiper.vue'
import SidebarNavigation from '@/components/SidebarNavigation.vue'

// 导入图片
import p7_1_1 from '@/assets/imgs/bg/p7-1-1.jpg'
import p7_1_2 from '@/assets/imgs/bg/p7-1-2.jpg'
import p7_1_3 from '@/assets/imgs/bg/p7-1-3.jpg'
import p7_1_4 from '@/assets/imgs/bg/p7-1-4.jpg'
import p7_1_5 from '@/assets/imgs/bg/p7-1-5.jpg'

// 导入tab按钮图片
import tab1Normal from '@/assets/imgs/p7/tab1.png'
import tab1Active from '@/assets/imgs/p7/tab1-active.png'
import tab2Normal from '@/assets/imgs/p7/tab2.png'
import tab2Active from '@/assets/imgs/p7/tab2-active.png'

// 世界观图片
const worldImages = [p7_1_1, p7_1_2, p7_1_3, p7_1_4, p7_1_5]

// 响应式数据
const currentSlide = ref(0)
const activeTab = ref('tab1')
const mainSwiperInstance = ref<any>(null)

// 角色故事数据
const storyData = reactive([
  {
    title: '传奇战士',
    description: '勇敢的战士踏上征程，为了守护家园而战',
    image: p7_1_1
  },
  {
    title: '神秘法师',
    description: '掌握古老魔法的智者，探寻世界的奥秘',
    image: p7_1_2
  },
  {
    title: '敏捷刺客',
    description: '隐藏在暗影中的杀手，执行秘密任务',
    image: p7_1_3
  },
  {
    title: '圣洁牧师',
    description: '治愈伤痛的圣者，为队友提供支援',
    image: p7_1_4
  },
  {
    title: '远程射手',
    description: '精准的弓箭手，百步穿杨的神射手',
    image: p7_1_5
  }
])

// 主 Swiper 事件处理
const onSlideChange = (index: number) => {
  currentSlide.value = index
}

const onMainSwiperReady = (swiper: any) => {
  console.log('主 Swiper 已准备就绪', swiper)
  mainSwiperInstance.value = swiper
}

// 世界探索 Swiper 事件处理
const onWorldSwiperChange = (index: number) => {
  console.log('世界探索轮播切换到:', index)
}

const onWorldSwiperReady = (swiper: any) => {
  console.log('世界探索 Swiper 已准备就绪', swiper)
}

// 故事 Swiper 事件处理
const onStorySwiperChange = (index: number) => {
  console.log('故事轮播切换到:', index)
}

const onStorySwiperReady = (swiper: any) => {
  console.log('故事 Swiper 已准备就绪', swiper)
}

// Tab 切换功能
const switchTab = (tab: string) => {
  activeTab.value = tab
}

// 获取tab按钮图片
const getTabImage = (tab: string) => {
  if (tab === 'tab1') {
    return activeTab.value === 'tab1' ? tab1Active : tab1Normal
  } else if (tab === 'tab2') {
    return activeTab.value === 'tab2' ? tab2Active : tab2Normal
  }
  return tab1Normal
}


</script>

<style lang="less" scoped>
.home-view {
  width: 100%;
  height: 100vh;
  min-width: 1200px;
  overflow: hidden;
  position: relative;
}

.slide-item {
  width: 100%;
  height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;

  // 为每个屏幕设置背景图
  &.slide-1 {
    background-image: url('@/assets/imgs/bg/p1.jpg');
  }

  &.slide-2 {
    background-image: url('@/assets/imgs/bg/p2.jpg');
  }

  &.slide-3 {
    background-image: url('@/assets/imgs/bg/p3.jpg');
  }

  &.slide-4 {
    background-image: url('@/assets/imgs/bg/p4.jpg');
  }

  &.slide-5 {
    background-image: url('@/assets/imgs/bg/p5.jpg');
  }

  &.slide-6 {
    background-image: url('@/assets/imgs/bg/p6.jpg');
  }

  &.slide-7 {
    background-color: #000;
  }
}

.slide-content {
  text-align: center;
  color: #fff;
  z-index: 2;
  position: relative;

  .slide-title {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    animation: fadeInUp 1s ease-out;

    @media (max-width: 768px) {
      font-size: 2.5rem;
    }
  }
}

// 第7屏特殊样式
.world-view-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 0;
  box-sizing: border-box;
  position: relative;
}

.tab-navigation {
  display: flex;
  gap: 2rem;
  margin: 2rem 0;
  z-index: 100;
  position: absolute;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);

  .tab-btn {
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    background-repeat: no-repeat;
    background-position: center;
    width: 351px;
    height: 89px;

    .active {
      width: 345px;
      height: 184px;
    }

    &:hover {
      transform: scale(1.05);
      filter: brightness(1.1);
    }
  }
}

// Tab切换动画
.tab-fade-enter-active,
.tab-fade-leave-active {
  transition: opacity 0.3s ease-in-out;
}

.tab-fade-enter-from,
.tab-fade-leave-to {
  opacity: 0;
}

.tab-content {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.tab-panel {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

// Tab1 - 水平全屏轮播样式
.tab1-panel {
  // 确保Tab1面板占据整个第7屏
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 10;
}

.horizontal-slide {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100vw;
  height: 100vh;
}

.horizontal-slide-content {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
  margin-top: 100px; // 为顶部Tab按钮留出空间
}

// Tab2 - 固定背景样式
.tab2-panel {
  background-image: url('@/assets/imgs/bg/p7-2.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 10;
}
</style>