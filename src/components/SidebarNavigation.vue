<template>
  <nav class="sidebar-navigation" role="navigation" aria-label="主导航">
    <ul class="nav-list">
      <li v-for="(item, index) in navigationItems" :key="index" class="nav-item"
        :class="{ active: activeIndex === index }">
        <img :src="getImageSrc(index)" :alt="item.label" loading="lazy" @click="handleNavigate(index)" />
      </li>
    </ul>
  </nav>
</template>

<script setup lang="ts">
// 静态导入所有图片资源
import normal1 from '@/assets/imgs/sidebar/normal/1.png'
import normal2 from '@/assets/imgs/sidebar/normal/2.png'
import normal3 from '@/assets/imgs/sidebar/normal/3.png'
import normal4 from '@/assets/imgs/sidebar/normal/4.png'
import normal5 from '@/assets/imgs/sidebar/normal/5.png'
import normal6 from '@/assets/imgs/sidebar/normal/6.png'
import normal7 from '@/assets/imgs/sidebar/normal/7.png'
import normal8 from '@/assets/imgs/sidebar/normal/8.png'

import active1 from '@/assets/imgs/sidebar/active/1.png'
import active2 from '@/assets/imgs/sidebar/active/2.png'
import active3 from '@/assets/imgs/sidebar/active/3.png'
import active4 from '@/assets/imgs/sidebar/active/4.png'
import active5 from '@/assets/imgs/sidebar/active/5.png'
import active6 from '@/assets/imgs/sidebar/active/6.png'
import active7 from '@/assets/imgs/sidebar/active/7.png'
import active8 from '@/assets/imgs/sidebar/active/8.png'

// Props 接口定义
interface Props {
  activeIndex: number // 当前激活的导航项索引（0-7）
}

// Emits 接口定义
interface Emits {
  (e: 'navigate', index: number): void // 导航项点击事件
}

// 定义 props 和 emits
const props = withDefaults(defineProps<Props>(), {
  activeIndex: 0
})

const emit = defineEmits<Emits>()

// 图片资源映射
const normalImages = [normal1, normal2, normal3, normal4, normal5, normal6, normal7, normal8]
const activeImages = [active1, active2, active3, active4, active5, active6, active7, active8]

// 导航项配置
const navigationItems = [
  { label: '首页' },
  { label: '预约' },
  { label: '抽奖' },
  { label: '里程碑' },
  { label: '职业介绍' },
  { label: '公会' },
  { label: '世界观' },
  { label: '游戏介绍' }
]

// 获取图片资源
const getImageSrc = (index: number): string => {
  const isActive = props.activeIndex === index
  return isActive ? activeImages[index] : normalImages[index]
}

// 处理导航点击事件
const handleNavigate = (index: number) => {
  if (index !== props.activeIndex) {
    emit('navigate', index)
  }
}
</script>

<style lang="less" scoped>
.sidebar-navigation {
  position: fixed;
  left: 30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;

  @media (max-width: 1024px) {
    display: none; // 在小屏幕上隐藏
  }
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 35px;
}

.nav-item {
  position: relative;
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  width: 123px;
  height: 22px;
  cursor: pointer;

  &.active {
    width: 219px;
    height: 93px;
    margin: -25px 0 -10px;

    img {
      position: absolute;
      left: -42px;
    }
  }

  img {
    width: auto;
    height: auto;
    object-fit: contain;
  }
}
</style>
