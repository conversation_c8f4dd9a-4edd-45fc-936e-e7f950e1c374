<template>
  <nav class="sidebar-navigation" role="navigation" aria-label="主导航">
    <ul class="nav-list">
      <li v-for="(item, index) in navigationItems" :key="index" class="nav-item"
        :class="{ active: activeIndex === index }">
        <img :src="getImageSrc(index)" :alt="item.label" loading="lazy" @click="handleNavigate(index)" />
      </li>
    </ul>
  </nav>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props 接口定义
interface Props {
  activeIndex: number // 当前激活的导航项索引（0-7）
}

// Emits 接口定义
interface Emits {
  (e: 'navigate', index: number): void // 导航项点击事件
}

// 定义 props 和 emits
const props = withDefaults(defineProps<Props>(), {
  activeIndex: 0
})

const emit = defineEmits<Emits>()

// 导航项配置
const navigationItems = [
  { label: '首页' },
  { label: '预约' },
  { label: '抽奖' },
  { label: '里程碑' },
  { label: '职业介绍' },
  { label: '公会' },
  { label: '世界探索' },
  { label: '角色故事' }
]

// 动态导入图片资源
const getImageSrc = (index: number): string => {
  const imageNumber = index + 1
  const state = props.activeIndex === index ? 'active' : 'normal'
  // 使用动态导入获取图片路径
  return new URL(`../assets/imgs/sidebar/${state}/${imageNumber}.png`, import.meta.url).href
}

// 处理导航点击事件
const handleNavigate = (index: number) => {
  if (index !== props.activeIndex) {
    emit('navigate', index)
  }
}
</script>

<style lang="less" scoped>
.sidebar-navigation {
  position: fixed;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;

  @media (max-width: 1024px) {
    display: none; // 在小屏幕上隐藏
  }
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.nav-item {
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  width: 219px;
  height: 93px;
  cursor: pointer;

  .active

  img {
    width: auto;
    height: auto;
    display: block;
    transition: all 0.3s ease;
    object-fit: contain;
  }
}
</style>
