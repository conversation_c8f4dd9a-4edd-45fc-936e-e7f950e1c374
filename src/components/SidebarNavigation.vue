<template>
  <nav class="sidebar-navigation" role="navigation" aria-label="主导航">
    <ul class="nav-list">
      <li v-for="(item, index) in navigationItems" :key="index" class="nav-item"
        :class="{ active: activeIndex === index }" :data-index="index + 1" :aria-label="item.label"
        @click="handleNavigate(index)">
      </li>
    </ul>
  </nav>
</template>

<script setup lang="ts">

// Props 接口定义
interface Props {
  activeIndex: number // 当前激活的导航项索引（0-7）
}

// Emits 接口定义
interface Emits {
  (e: 'navigate', index: number): void // 导航项点击事件
}

// 定义 props 和 emits
const props = withDefaults(defineProps<Props>(), {
  activeIndex: 0
})

const emit = defineEmits<Emits>()

// 导航项配置
const navigationItems = [
  { label: '首页' },
  { label: '预约' },
  { label: '抽奖' },
  { label: '里程碑' },
  { label: '职业介绍' },
  { label: '公会' },
  { label: '世界观' },
  { label: '游戏介绍' }
]

// 处理导航点击事件
const handleNavigate = (index: number) => {
  if (index !== props.activeIndex) {
    emit('navigate', index)
  }
}
</script>

<style lang="less" scoped>
.sidebar-navigation {
  position: fixed;
  left: 30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;

  @media (max-width: 750px) {
    display: none; // 在小屏幕上隐藏
  }
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 35px;
}

.nav-item {
  position: relative;
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  width: 123px;
  height: 22px;
  cursor: pointer;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s ease;

  // 默认状态背景图
  &[data-index="1"] {
    background-image: url('@/assets/imgs/sidebar/normal/1.png');
  }

  &[data-index="2"] {
    background-image: url('@/assets/imgs/sidebar/normal/2.png');
  }

  &[data-index="3"] {
    background-image: url('@/assets/imgs/sidebar/normal/3.png');
  }

  &[data-index="4"] {
    background-image: url('@/assets/imgs/sidebar/normal/4.png');
  }

  &[data-index="5"] {
    background-image: url('@/assets/imgs/sidebar/normal/5.png');
  }

  &[data-index="6"] {
    background-image: url('@/assets/imgs/sidebar/normal/6.png');
  }

  &[data-index="7"] {
    background-image: url('@/assets/imgs/sidebar/normal/7.png');
  }

  &[data-index="8"] {
    background-image: url('@/assets/imgs/sidebar/normal/8.png');
  }

  // 激活状态
  &.active {
    width: 219px;
    height: 93px;
    margin: -35px 0 -20px;
    margin-left: -42px;

    // 激活状态背景图
    &[data-index="1"] {
      background-image: url('@/assets/imgs/sidebar/active/1.png');
    }

    &[data-index="2"] {
      background-image: url('@/assets/imgs/sidebar/active/2.png');
    }

    &[data-index="3"] {
      background-image: url('@/assets/imgs/sidebar/active/3.png');
    }

    &[data-index="4"] {
      background-image: url('@/assets/imgs/sidebar/active/4.png');
    }

    &[data-index="5"] {
      background-image: url('@/assets/imgs/sidebar/active/5.png');
    }

    &[data-index="6"] {
      background-image: url('@/assets/imgs/sidebar/active/6.png');
    }

    &[data-index="7"] {
      background-image: url('@/assets/imgs/sidebar/active/7.png');
    }

    &[data-index="8"] {
      background-image: url('@/assets/imgs/sidebar/active/8.png');
    }
  }

  // 悬停效果
  &:hover {
    transform: translateX(2px);
    filter: brightness(1.1);
  }

  // 点击效果
  &:active {
    transform: scale(0.95) translateX(2px);
  }
}
</style>
