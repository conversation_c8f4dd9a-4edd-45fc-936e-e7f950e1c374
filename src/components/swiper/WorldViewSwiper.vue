<template>
  <div class="horizontal-swiper-container">
    <BaseSwiper direction="horizontal" :slides-per-view="1" :space-between="0" :speed="1000"
      :autoplay="{ delay: 4000, disableOnInteraction: false }" :navigation="true" :modules="modules" effect="fade"
      @swiper-ready="onSwiperReady" @slide-change="onSlideChange">
      <slot />
    </BaseSwiper>
  </div>
</template>

<script setup lang="ts">
import BaseSwiper from './BaseSwiper.vue'
import { useSwiper } from '@/hooks/useSwiper'
import { Navigation, Autoplay, EffectFade } from 'swiper/modules'

const emit = defineEmits<{
  slideChange: [index: number]
  swiperReady: [swiper: any]
}>()

// 使用基础 Swiper Hook
const {
  swiperInstance,
  currentSlide,
  isReady,
  goToSlide,
  startAutoplay,
  stopAutoplay,
  modules
} = useSwiper([Navigation, Autoplay, EffectFade])

// Swiper 初始化回调
const onSwiperReady = (swiper: any) => {
  emit('swiperReady', swiper)
}

// 滑动变化回调
const onSlideChange = (swiper: any) => {
  emit('slideChange', swiper.activeIndex)
}

// 暴露方法给父组件
defineExpose({
  swiperInstance,
  currentSlide,
  isReady,
  goToSlide,
  startAutoplay,
  stopAutoplay
})
</script>

<style lang="less" scoped>
.horizontal-swiper-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.horizontal-swiper {
  width: 100%;
  height: 100%;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

:deep(.swiper-button-next),
:deep(.swiper-button-prev) {
  width: 83px;
  height: 85px;
  z-index: 50;
  background: url(@/assets/imgs/p7/tab1-arrow.png);

  &:hover {
    background: url(@/assets/imgs/p7/tab1-arrow.png);
  }

  &:after {
    display: none;
  }
}

:deep(.swiper-button-prev) {
  transform: rotate(180deg);
  left: 18%;
}

:deep(.swiper-button-next) {
  right: 18%;
}
</style>
